# Spring Cloud Gateway 参数转换功能

## 功能概述

本功能为Spring Cloud Gateway提供了强大的参数转换能力，支持在网关层对请求和响应参数进行灵活的映射和转换。

## 主要特性

1. **请求参数转换**：支持header、query、body参数的转换
2. **响应参数转换**：支持响应体字段的映射和转换
3. **多种数据源**：支持从header、query、body、const（常量）、sys（系统参数）获取数据
4. **表达式支持**：支持简单的逻辑表达式，如三元运算符、默认值函数等
5. **动态配置**：支持运行时动态添加、修改、删除配置
6. **路径匹配**：支持基于路径模式的配置匹配

## 配置说明

### 启用功能

在 `application.yml` 中添加以下配置：

```yaml
gateway:
  parameter:
    transform:
      enabled: true  # 启用参数转换功能
      config:
        location: classpath:parameter-mapping-config.json  # 配置文件位置
```

### DSL配置格式

```json
{
  "request": {
    "mapping": {
      "header": {
        "目标字段名": "源字段表达式"
      },
      "query": {
        "目标字段名": "源字段表达式"
      },
      "body": {
        "目标字段名": "源字段表达式"
      }
    }
  },
  "response": {
    "mapping": {
      "目标字段名": "源字段表达式"
    }
  }
}
```

### 表达式语法

#### 基本表达式

- `header.字段名`：从请求头获取值
- `query.字段名`：从查询参数获取值
- `body.字段名`：从请求体获取值（支持嵌套，如 `body.user.name`）
- `const.值`：常量值
- `sys.参数名`：系统参数（目前从请求头获取）

#### 复杂表达式

使用 `${}` 包围复杂表达式：

1. **三元运算符**：`${body.dId ? 1 : 0}`
   - 如果 body.dId 为真，返回 1，否则返回 0

2. **默认值函数**：`${defaultEmpty(body.requestId, '11111')}`
   - 如果 body.requestId 为空，返回默认值 '11111'

## 使用示例

### 配置示例

```json
{
  "request": {
    "mapping": {
      "header": {
        "headerTest": "header.user-key"
      },
      "query": {
        "scene": "query.device"
      },
      "body": {
        "scene": "body.device",
        "bizCode": "const.固定编码示例",
        "userId": "sys.userId",
        "diseaseDictId": "${body.dId ? 1 : 0}",
        "requestId": "${defaultEmpty(body.requestId, '11111')}"
      }
    }
  },
  "response": {
    "mapping": {
      "subMsg": "subMessage",
      "data.bizID": "data.id",
      "data.bizConst": "const.12090",
      "data.sysTest": "sys.sysParam"
    }
  }
}
```

### 在Gateway路由中使用

```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: example-service
          uri: lb://example-service
          predicates:
            - Path=/api/example/**
          filters:
            - ParameterTransform  # 添加参数转换过滤器
            - StripPrefix=1
```

## 管理API

提供了REST API来动态管理配置：

### 获取所有配置
```
GET /gateway/parameter-transform/configs
```

### 根据路径获取配置
```
GET /gateway/parameter-transform/config?path=/api/example
```

### 添加或更新配置
```
POST /gateway/parameter-transform/config?pathPattern=/api/example/**
Content-Type: application/json

{配置JSON}
```

### 删除配置
```
DELETE /gateway/parameter-transform/config?pathPattern=/api/example/**
```

### 重新加载配置
```
POST /gateway/parameter-transform/reload
```

### 获取功能状态
```
GET /gateway/parameter-transform/status
```

## 注意事项

1. **性能考虑**：参数转换会增加一定的处理开销，建议在必要时才启用
2. **表达式安全**：当前实现的表达式求值器相对简单，不支持复杂的脚本执行
3. **配置缓存**：配置会被缓存在内存中，修改配置文件后需要重新加载
4. **错误处理**：转换过程中的错误会被记录日志，但不会中断请求流程

## 扩展开发

### 自定义表达式函数

可以通过扩展 `ExpressionEvaluator` 类来添加更多的表达式函数。

### 自定义配置加载器

可以实现自定义的配置加载逻辑，比如从数据库、配置中心等加载配置。

### 自定义转换器

可以实现 `ParameterTransformer` 接口来提供自定义的转换逻辑。
