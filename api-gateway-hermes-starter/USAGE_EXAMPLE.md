# 参数转换功能使用示例

## 快速开始

### 1. 启用功能

在 `application.yml` 中设置：

```yaml
gateway:
  parameter:
    transform:
      enabled: true
```

### 2. 配置路由

```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: example-api
          uri: lb://example-service
          predicates:
            - Path=/api/example/**
          filters:
            - ParameterTransform  # 添加参数转换过滤器
            - StripPrefix=1
```

### 3. 配置转换规则

创建或修改 `src/main/resources/parameter-mapping-config.json`：

```json
{
  "request": {
    "mapping": {
      "header": {
        "X-User-ID": "header.Authorization"
      },
      "query": {
        "deviceType": "query.device"
      },
      "body": {
        "userId": "sys.userId",
        "deviceInfo": "body.device",
        "bizCode": "const.EXAMPLE_BIZ",
        "status": "${body.active ? 1 : 0}",
        "requestId": "${defaultEmpty(body.requestId, 'auto-generated')}"
      }
    }
  },
  "response": {
    "mapping": {
      "message": "msg",
      "data.userId": "data.user_id",
      "data.timestamp": "const.2025-07-24"
    }
  }
}
```

## 测试示例

### 原始请求

```bash
curl -X POST http://localhost:8089/api/example/user/info \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer token123" \
  -H "userId: 12345" \
  -d '{
    "device": "mobile",
    "active": true,
    "requestId": ""
  }' \
  "?device=smartphone"
```

### 转换后发送给下游服务的请求

**Headers:**
```
X-User-ID: Bearer token123
userId: 12345
```

**Query Parameters:**
```
deviceType=smartphone
```

**Body:**
```json
{
  "userId": "12345",
  "deviceInfo": "mobile", 
  "bizCode": "EXAMPLE_BIZ",
  "status": 1,
  "requestId": "auto-generated"
}
```

### 下游服务返回的响应

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "user_id": "12345",
    "name": "张三"
  }
}
```

### 转换后返回给客户端的响应

```json
{
  "message": "success",
  "data": {
    "userId": "12345",
    "timestamp": "2025-07-24"
  }
}
```

## 动态配置管理

### 查看当前配置

```bash
curl http://localhost:8089/gateway/parameter-transform/configs
```

### 添加新配置

```bash
curl -X POST "http://localhost:8089/gateway/parameter-transform/config?pathPattern=/api/new/**" \
  -H "Content-Type: application/json" \
  -d '{
    "request": {
      "mapping": {
        "body": {
          "newField": "const.NEW_VALUE"
        }
      }
    }
  }'
```

### 重新加载配置

```bash
curl -X POST http://localhost:8089/gateway/parameter-transform/reload
```

## 常见使用场景

### 1. API版本兼容

将新版本API的字段映射到旧版本：

```json
{
  "request": {
    "mapping": {
      "body": {
        "user_name": "body.userName",
        "user_id": "body.userId"
      }
    }
  }
}
```

### 2. 添加统一字段

为所有请求添加统一的业务字段：

```json
{
  "request": {
    "mapping": {
      "body": {
        "appId": "const.MOBILE_APP",
        "version": "const.1.0.0",
        "timestamp": "sys.timestamp"
      }
    }
  }
}
```

### 3. 响应字段过滤

只返回客户端需要的字段：

```json
{
  "response": {
    "mapping": {
      "userId": "data.user.id",
      "userName": "data.user.name",
      "status": "data.user.status"
    }
  }
}
```

## 注意事项

1. **性能影响**：参数转换会增加请求处理时间，建议只在必要时使用
2. **配置缓存**：配置修改后需要重新加载才能生效
3. **错误处理**：转换失败时会记录日志但不会中断请求
4. **表达式限制**：当前支持的表达式相对简单，复杂逻辑建议在业务层处理
