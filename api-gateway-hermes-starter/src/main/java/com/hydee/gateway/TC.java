package com.hydee.gateway;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.dto.req.SetAuthInfoReq;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceApiResp;
import com.yxt.safecenter.feign.sdk.api.SafeInterfaceApi;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;

/**
 * Since: 2025/07/23 14:38
 * Author: qs
 */

@RequestMapping(value = "/ttt")
public class TC {


    @Resource
    private SafeInterfaceApi safeInterfaceApi;

    @GetMapping(value = "/inter")
    public ResponseBase<Void> setAuthInfo(String a) {
        ResponseBase<List<SafeInterfaceApiResp>> listResponseBase = safeInterfaceApi.listByApplicationName(a);
        return null;
    }
}
