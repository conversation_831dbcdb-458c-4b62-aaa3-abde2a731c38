package com.hydee.gateway.transform.config;

import com.hydee.gateway.transform.core.DefaultParameterTransformer;
import com.hydee.gateway.transform.core.ExpressionEvaluator;
import com.hydee.gateway.transform.core.ParameterTransformer;
import com.hydee.gateway.transform.filter.ParameterTransformGatewayFilterFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;

/**
 * 参数转换自动配置类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Configuration
@ConditionalOnProperty(name = "gateway.parameter.transform.enabled", havingValue = "true", matchIfMissing = false)
public class ParameterTransformAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public ExpressionEvaluator expressionEvaluator() {
        return new ExpressionEvaluator();
    }
    
    @Bean
    @ConditionalOnMissingBean
    public ParameterTransformer parameterTransformer(ExpressionEvaluator expressionEvaluator) {
        return new DefaultParameterTransformer(expressionEvaluator);
    }
    
    @Bean
    @ConditionalOnMissingBean
    public ParameterMappingConfigManager parameterMappingConfigManager(ResourceLoader resourceLoader) {
        return new ParameterMappingConfigManager(resourceLoader);
    }
    
    @Bean
    @ConditionalOnMissingBean
    public ParameterTransformGatewayFilterFactory parameterTransformGatewayFilterFactory(
            ParameterMappingConfigManager configManager,
            ParameterTransformer parameterTransformer) {
        ParameterTransformGatewayFilterFactory factory = new ParameterTransformGatewayFilterFactory(configManager, parameterTransformer);
        return factory;
    }
}
