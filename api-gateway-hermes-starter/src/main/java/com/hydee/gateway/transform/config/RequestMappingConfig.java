package com.hydee.gateway.transform.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * 请求参数映射配置
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RequestMappingConfig {
    
    /**
     * 参数映射规则
     */
    private MappingRules mapping;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MappingRules {
        
        /**
         * 请求头映射规则
         * key: 目标字段名, value: 源字段表达式
         */
        private Map<String, String> header;
        
        /**
         * 查询参数映射规则
         * key: 目标字段名, value: 源字段表达式
         */
        private Map<String, String> query;
        
        /**
         * 请求体映射规则
         * key: 目标字段名, value: 源字段表达式
         */
        private Map<String, String> body;
    }
}
