package com.hydee.gateway.transform.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hydee.gateway.transform.config.ParameterMappingConfig;
import com.hydee.gateway.transform.config.ParameterMappingConfigManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 参数转换管理控制器
 * 提供配置管理的REST API
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/gateway/parameter-transform")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "gateway.parameter.transform.enabled", havingValue = "true")
public class ParameterTransformController {
    
    private final ParameterMappingConfigManager configManager;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 获取所有配置
     */
    @GetMapping("/configs")
    public Map<String, ParameterMappingConfig> getAllConfigs() {
        return configManager.getAllConfigs();
    }
    
    /**
     * 根据路径获取配置
     */
    @GetMapping("/config")
    public ParameterMappingConfig getConfig(@RequestParam String path) {
        return configManager.getConfig(path);
    }
    
    /**
     * 添加或更新配置
     */
    @PostMapping("/config")
    public String addConfig(@RequestParam String pathPattern, 
                           @RequestBody ParameterMappingConfig config) {
        try {
            configManager.addConfig(pathPattern, config);
            return "配置添加成功";
        } catch (Exception e) {
            log.error("添加配置失败", e);
            return "配置添加失败: " + e.getMessage();
        }
    }
    
    /**
     * 删除配置
     */
    @DeleteMapping("/config")
    public String removeConfig(@RequestParam String pathPattern) {
        try {
            configManager.removeConfig(pathPattern);
            return "配置删除成功";
        } catch (Exception e) {
            log.error("删除配置失败", e);
            return "配置删除失败: " + e.getMessage();
        }
    }
    
    /**
     * 重新加载配置
     */
    @PostMapping("/reload")
    public String reloadConfigs() {
        try {
            configManager.reloadConfigurations();
            return "配置重新加载成功";
        } catch (Exception e) {
            log.error("重新加载配置失败", e);
            return "配置重新加载失败: " + e.getMessage();
        }
    }
    
    /**
     * 清空所有配置
     */
    @DeleteMapping("/configs")
    public String clearConfigs() {
        try {
            configManager.clearConfigs();
            return "所有配置清空成功";
        } catch (Exception e) {
            log.error("清空配置失败", e);
            return "清空配置失败: " + e.getMessage();
        }
    }
    
//    /**
//     * 获取转换功能状态
//     */
//    @GetMapping("/status")
//    public Map<String, Object> getStatus() {
//        return Map.of(
//            "enabled", configManager.isTransformEnabled(),
//            "configCount", configManager.getAllConfigs().size()
//        );
//    }
}
