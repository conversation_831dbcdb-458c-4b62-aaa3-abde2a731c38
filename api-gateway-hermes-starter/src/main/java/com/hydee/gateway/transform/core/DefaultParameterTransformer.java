package com.hydee.gateway.transform.core;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hydee.gateway.transform.config.ParameterMappingConfig;
import com.hydee.gateway.transform.config.RequestMappingConfig;
import com.hydee.gateway.transform.config.ResponseMappingConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 默认参数转换器实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultParameterTransformer implements ParameterTransformer {
    
    private final ExpressionEvaluator expressionEvaluator;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Mono<ServerWebExchange> transformRequest(ServerWebExchange exchange, ParameterMappingConfig config) {
        if (config == null || config.getRequest() == null) {
            return Mono.just(exchange);
        }
        
        return readRequestBody(exchange)
            .flatMap(requestBody -> {
                try {
                    JsonNode requestBodyJson = null;
                    if (StringUtils.hasText(requestBody)) {
                        requestBodyJson = objectMapper.readTree(requestBody);
                    }
                    
                    ServerHttpRequest transformedRequest = transformRequestInternal(
                        exchange.getRequest(), config.getRequest(), exchange, requestBodyJson);
                    
                    return Mono.just(exchange.mutate().request(transformedRequest).build());
                } catch (Exception e) {
                    log.error("请求参数转换失败", e);
                    return Mono.just(exchange);
                }
            });
    }
    
    @Override
    public Mono<String> transformResponse(String originalResponse, ParameterMappingConfig config, ServerWebExchange exchange) {
        if (config == null || config.getResponse() == null || !StringUtils.hasText(originalResponse)) {
            return Mono.just(originalResponse);
        }
        
        try {
            JsonNode responseJson = objectMapper.readTree(originalResponse);
            JsonNode transformedJson = transformResponseInternal(responseJson, config.getResponse(), exchange);
            return Mono.just(objectMapper.writeValueAsString(transformedJson));
        } catch (Exception e) {
            log.error("响应参数转换失败", e);
            return Mono.just(originalResponse);
        }
    }
    
    /**
     * 读取请求体
     */
    private Mono<String> readRequestBody(ServerWebExchange exchange) {
        return exchange.getRequest().getBody()
            .map(dataBuffer -> {
                byte[] bytes = new byte[dataBuffer.readableByteCount()];
                dataBuffer.read(bytes);
                return new String(bytes, StandardCharsets.UTF_8);
            })
            .reduce(StringBuilder::new, StringBuilder::append)
            .map(StringBuilder::toString)
            .defaultIfEmpty("");
    }
    
    /**
     * 转换请求内部实现
     */
    private ServerHttpRequest transformRequestInternal(ServerHttpRequest originalRequest, 
                                                     RequestMappingConfig requestConfig,
                                                     ServerWebExchange exchange,
                                                     JsonNode requestBodyJson) {
        
        RequestMappingConfig.MappingRules mapping = requestConfig.getMapping();
        if (mapping == null) {
            return originalRequest;
        }
        
        // 构建新的请求
        ServerHttpRequest.Builder requestBuilder = originalRequest.mutate();
        
        // 转换请求头
        if (mapping.getHeader() != null) {
            HttpHeaders newHeaders = transformHeaders(mapping.getHeader(), exchange, requestBodyJson);
            newHeaders.forEach((name, values) -> {
                requestBuilder.header(name, values.toArray(new String[0]));
            });
        }
        
        // 转换查询参数
        if (mapping.getQuery() != null) {
            URI newUri = transformQueryParams(originalRequest.getURI(), mapping.getQuery(), exchange, requestBodyJson);
            requestBuilder.uri(newUri);
        }
        
        // 转换请求体
        if (mapping.getBody() != null) {
            String newBody = transformRequestBody(requestBodyJson, mapping.getBody(), exchange);
            if (StringUtils.hasText(newBody)) {
                // 注意：这里需要特殊处理，因为我们需要返回一个装饰器来处理请求体
                return new ServerHttpRequestDecorator(requestBuilder.build()) {
                    @Override
                    public Flux<DataBuffer> getBody() {
                        DataBufferFactory bufferFactory = exchange.getResponse().bufferFactory();
                        DataBuffer buffer = bufferFactory.wrap(newBody.getBytes(StandardCharsets.UTF_8));
                        return Flux.just(buffer);
                    }
                };
            }
        }
        
        return new ServerHttpRequestDecorator(requestBuilder.build());
    }
    
    /**
     * 转换请求头
     */
    private HttpHeaders transformHeaders(Map<String, String> headerMapping, 
                                       ServerWebExchange exchange, 
                                       JsonNode requestBodyJson) {
        HttpHeaders newHeaders = new HttpHeaders();
        
        for (Map.Entry<String, String> entry : headerMapping.entrySet()) {
            String targetHeader = entry.getKey();
            String sourceExpression = entry.getValue();
            
            Object value = expressionEvaluator.evaluateExpression(sourceExpression, exchange, requestBodyJson);
            if (value != null) {
                newHeaders.add(targetHeader, value.toString());
            }
        }
        
        return newHeaders;
    }
    
    /**
     * 转换查询参数
     */
    private URI transformQueryParams(URI originalUri, 
                                   Map<String, String> queryMapping,
                                   ServerWebExchange exchange,
                                   JsonNode requestBodyJson) {
        
        UriComponentsBuilder builder = UriComponentsBuilder.fromUri(originalUri);
        
        for (Map.Entry<String, String> entry : queryMapping.entrySet()) {
            String targetParam = entry.getKey();
            String sourceExpression = entry.getValue();
            
            Object value = expressionEvaluator.evaluateExpression(sourceExpression, exchange, requestBodyJson);
            if (value != null) {
                builder.queryParam(targetParam, value.toString());
            }
        }
        
        return builder.build().toUri();
    }
    
    /**
     * 转换请求体
     */
    private String transformRequestBody(JsonNode originalBody,
                                      Map<String, String> bodyMapping,
                                      ServerWebExchange exchange) {
        try {
            ObjectNode newBody = objectMapper.createObjectNode();
            
            for (Map.Entry<String, String> entry : bodyMapping.entrySet()) {
                String targetField = entry.getKey();
                String sourceExpression = entry.getValue();
                
                Object value = expressionEvaluator.evaluateExpression(sourceExpression, exchange, originalBody);
                if (value != null) {
                    setNestedValue(newBody, targetField, value);
                }
            }
            
            return objectMapper.writeValueAsString(newBody);
        } catch (Exception e) {
            log.error("转换请求体失败", e);
            return originalBody != null ? originalBody.toString() : "{}";
        }
    }
    
    /**
     * 转换响应内部实现
     */
    private JsonNode transformResponseInternal(JsonNode originalResponse,
                                             ResponseMappingConfig responseConfig,
                                             ServerWebExchange exchange) {
        try {
            ObjectNode newResponse = objectMapper.createObjectNode();
            
            Map<String, String> mapping = responseConfig.getMapping();
            if (mapping != null) {
                for (Map.Entry<String, String> entry : mapping.entrySet()) {
                    String targetField = entry.getKey();
                    String sourceExpression = entry.getValue();
                    
                    Object value = evaluateResponseExpression(sourceExpression, originalResponse, exchange);
                    if (value != null) {
                        setNestedValue(newResponse, targetField, value);
                    }
                }
            }
            
            return newResponse;
        } catch (Exception e) {
            log.error("转换响应失败", e);
            return originalResponse;
        }
    }
    
    /**
     * 评估响应表达式
     */
    private Object evaluateResponseExpression(String expression, JsonNode responseBody, ServerWebExchange exchange) {
        if (expression.startsWith("const.")) {
            return expression.substring(6);
        } else if (expression.startsWith("sys.")) {
            String sysParam = expression.substring(4);
            return getSystemParameter(sysParam, exchange);
        } else {
            // 从响应体中获取值
            return getValueFromJson(responseBody, expression);
        }
    }
    
    /**
     * 从JSON中获取值
     */
    private Object getValueFromJson(JsonNode json, String path) {
        if (json == null) {
            return null;
        }
        
        String[] pathParts = path.split("\\.");
        JsonNode current = json;
        
        for (String part : pathParts) {
            if (current == null || !current.has(part)) {
                return null;
            }
            current = current.get(part);
        }
        
        if (current.isTextual()) {
            return current.asText();
        } else if (current.isNumber()) {
            return current.asLong();
        } else if (current.isBoolean()) {
            return current.asBoolean();
        } else {
            return current.toString();
        }
    }
    
    /**
     * 设置嵌套值
     */
    private void setNestedValue(ObjectNode node, String path, Object value) {
        String[] pathParts = path.split("\\.");
        ObjectNode current = node;
        
        for (int i = 0; i < pathParts.length - 1; i++) {
            String part = pathParts[i];
            if (!current.has(part)) {
                current.set(part, objectMapper.createObjectNode());
            }
            current = (ObjectNode) current.get(part);
        }
        
        String lastPart = pathParts[pathParts.length - 1];
        if (value instanceof String) {
            current.put(lastPart, (String) value);
        } else if (value instanceof Number) {
            current.put(lastPart, ((Number) value).longValue());
        } else if (value instanceof Boolean) {
            current.put(lastPart, (Boolean) value);
        } else {
            current.put(lastPart, value.toString());
        }
    }
    
    /**
     * 获取系统参数
     */
    private Object getSystemParameter(String paramName, ServerWebExchange exchange) {
        HttpHeaders headers = exchange.getRequest().getHeaders();
        return headers.getFirst(paramName);
    }
}
