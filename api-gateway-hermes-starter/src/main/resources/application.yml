server:
  port: 8089
  tomcat:
    keep-alive-timeout: 3000

reactive:
  feign:
    client:
      default:
        # 最大连接数
        pool:
          max-connections: 200
          pending-acquire: 500
reactor:
  netty:
    transport:
      native:
        epoll-enabled: true
  feign:
    loadbalancer:
      enabled: true
    client:
      config:
        default:
          readTimeoutMillis: 3000
          connectTimeoutMillis: 5000
spring:
  reactor:
    netty:
      tcp:
        auto-read: true  # 直接生效，无需代码
  codec:
    max-in-memory-size: 512KB
  application:
    name: api-gateway-hermes
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    loadbalancer:
      retry:
        enabled: true
      cache:
        enabled: true
        ttl: 5s
      health-check:
        interval: 5s
        path:
          hydee-middle-member: /actuator/health
          hydee-middle-market: /actuator/health

    ## 流量控制配置
    sentinel:
      filter:
        enabled: false
      scg:
        fallback:
          enabled: true
          ## response返回文字提示信息，
          mode: response
          response-status: 200
          response-body: '{"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}'
          content-type: application/json
      transport:
        dashboard: sentinel:8858
      eager: true
      data-source:
        ## 配置流控规则，名字任意
        flow:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            ## 配置ID
            dataId: ${spring.application.name}-gateway-flow-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: gw-flow
        ## 配置流控规则，名字任意
        api:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            ## 配置ID
            dataId: ${spring.application.name}-gateway-api-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: gw-api-group
        ## 配置降级规则，名字任意
        degrade:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            ## 配置ID
            dataId: ${spring.application.name}-degrade-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: degrade
    discovery:
      client:
        health-indicator:
          enabled: false
    nacos:
      discovery:
        server-addr: http://10.4.3.210:8848;
        namespace: fca9f3f3-c2a7-49ad-a3ea-d5fe988ceb30
        metadata:
          department: NR
        register-enabled: false
        weight: 0
    inetutils:
      preferred-networks:
        - ^10\.200.+
    gateway:
      httpclient:
        wiretap: true
        use-native-transport: false
        connect-timeout: 2000   # 1秒连接超时
        response-timeout: 20s    # 3秒响应超时
        pool:
          max-idle-time: PT10S   # 3秒最大空闲时间
          eviction-interval: PT30S   # 3秒清理间隔
          max-connections: 5000  # 进一步增加最大连接数
          acquire-timeout: 3    # 减少获取连接超时时间
          type: FIXED           # 使用固定大小的连接池
      # 添加超时配置
      http-client:
        wiretap: false
        ssl:
          use-insecure-trust-manager: true
        connect-timeout: 1000
        response-timeout: 3000
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOriginPatterns: "*"
            #            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
      routes:
        - id: yxt-safe-center
          uri: lb://yxt-safe-center
          predicates:
            - Path=/safe-center/**
          filters:
            - StripPrefix=1
    openfeign:
      client:
        config:
          default:
            enableHttp2: false
            followRedirects: false
            disableRequestBuffering: true  # 避免大请求卡住'
      reactive:
        buffering:
          enabled: true  # 启用缓冲
          chunked-transfer: false  # 禁用分块传输解码

  redis:
    password: yxt_redis123
    jedis:
      pool:
        min-idle: 10
        max-active: 200
        max-idle: 50
        max-wait: 1000
    timeout: 1000
    cluster:
      nodes: **********:9000,**********:9001,**********:9002,**********:9003,**********:9004,**********:9005     # 此处新增，集群地址（此处为测试环境地址）
      max-redirects: 3
  # 自定义Redis配置
  custom-redis:
    password: yxt_redis123
    timeout: 1000
    cluster:
      nodes: **********:9000,**********:9001,**********:9002,**********:9003,**********:9004,**********:9005
      max-redirects: 3
api:
  base-info-version: 1.0
  version: 1.0

management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
    metrics:
      enabled: true
    health:
      enabled: true
      #never
      show-details: always
    gateway:
      enabled: false
  health:
    defaults:
      enabled: false
  endpoints:
    web:
      exposure:
        include: ["*"]
      path-mapping:
        health: /actuator/health

feign:
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 10000  # 10秒连接超时
        readTimeout: 30000     # 30秒读取超时
        loggerLevel: full

rest:
  connectTimeout: 3000
  readTimeout: 6000
jwt:
  expire: 14400
  rsa-secret: xx1WET12^%3^(WE45
auth:
  user:
    token-header: Authorization

logging:
  level:
    root: INFO
    io.netty: DEBUG
    org.springframework.web.reactive: DEBUG
    reactor.netty.http.client: TRACE  # 关键！记录网络层收发细节
    feign: DEBUG
    reactor.netty: DEBUG
    io.github.resilience4j: DEBUG

token:
  resolver:
    #        igrone:
    #            mercodes: 999999,hydee,SPHYDEE
    body:
      enable: true

alarm:
  robot:
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: ***********
  sendErrorLog:
    enable: false

business:
  order:
    kcpos:
      url: http://hydee-business-order.svc.k8s.dev.hxyxt.com
      old_url: http://**********:8080
    hdpos:
      url: http://hydee-business-order.svc.k8s.dev.hxyxt.com
    open:
      storeIds: qwe     #已切店id号

grey:
  enable: true
  local-mappings:
    '[(.+)]': $1.svc.k8s.dev.hxyxt.com
#    'yxt-safe-center': localhost:8080

safe-center:
  gateway-channel: C
  auth:
    enable:
      list: yxt-safe-center,yxt-ai-assistant,ydjia-merchant-customer

# 参数转换配置
gateway:
  parameter:
    transform:
      enabled: false  # 默认关闭，需要时开启
      config:
        location: classpath:parameter-mapping-config.json