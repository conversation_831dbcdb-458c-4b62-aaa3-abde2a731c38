package com.hydee.gateway.transform;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hydee.gateway.transform.core.ExpressionEvaluator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 表达式求值器测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
public class ExpressionEvaluatorTest {
    
    private ExpressionEvaluator evaluator;
    private ObjectMapper objectMapper;
    private ServerWebExchange exchange;
    private JsonNode requestBody;
    
    @BeforeEach
    public void setUp() throws Exception {
        evaluator = new ExpressionEvaluator();
        objectMapper = new ObjectMapper();
        
        // 创建模拟的请求
        ServerHttpRequest request = MockServerHttpRequest
            .get("/test")
            .header("user-key", "test-user")
            .header("userId", "12345")
            .queryParam("device", "mobile")
            .build();
        
//        exchange = MockServerWebExchange.from(request);
        
        // 创建模拟的请求体
        String jsonBody = "{\"device\":\"mobile\",\"dId\":true,\"requestId\":\"\",\"user\":{\"name\":\"张三\"}}";
        requestBody = objectMapper.readTree(jsonBody);
    }
    
    @Test
    public void testHeaderExpression() {
        Object result = evaluator.evaluateExpression("header.user-key", exchange, requestBody);
        assertEquals("test-user", result);
    }
    
    @Test
    public void testQueryExpression() {
        Object result = evaluator.evaluateExpression("query.device", exchange, requestBody);
        assertEquals("mobile", result);
    }
    
    @Test
    public void testBodyExpression() {
        Object result = evaluator.evaluateExpression("body.device", exchange, requestBody);
        assertEquals("mobile", result);
    }
    
    @Test
    public void testNestedBodyExpression() {
        Object result = evaluator.evaluateExpression("body.user.name", exchange, requestBody);
        assertEquals("张三", result);
    }
    
    @Test
    public void testConstExpression() {
        Object result = evaluator.evaluateExpression("const.固定编码示例", exchange, requestBody);
        assertEquals("固定编码示例", result);
    }
    
    @Test
    public void testSysExpression() {
        Object result = evaluator.evaluateExpression("sys.userId", exchange, requestBody);
        assertEquals("12345", result);
    }
    
    @Test
    public void testTernaryExpressionTrue() {
        Object result = evaluator.evaluateExpression("${body.dId ? 1 : 0}", exchange, requestBody);
        assertEquals(1L, result);
    }
    
    @Test
    public void testTernaryExpressionFalse() throws Exception {
        // 修改请求体，使dId为false
        String jsonBody = "{\"device\":\"mobile\",\"dId\":false,\"requestId\":\"\"}";
        JsonNode falseRequestBody = objectMapper.readTree(jsonBody);
        
        Object result = evaluator.evaluateExpression("${body.dId ? 1 : 0}", exchange, falseRequestBody);
        assertEquals(0L, result);
    }
    
    @Test
    public void testDefaultEmptyWithEmptyValue() {
        Object result = evaluator.evaluateExpression("${defaultEmpty(body.requestId, '11111')}", exchange, requestBody);
        assertEquals("11111", result);
    }
    
    @Test
    public void testDefaultEmptyWithNonEmptyValue() throws Exception {
        // 修改请求体，使requestId有值
        String jsonBody = "{\"device\":\"mobile\",\"dId\":true,\"requestId\":\"22222\"}";
        JsonNode nonEmptyRequestBody = objectMapper.readTree(jsonBody);
        
        Object result = evaluator.evaluateExpression("${defaultEmpty(body.requestId, '11111')}", exchange, nonEmptyRequestBody);
        assertEquals("22222", result);
    }
    
    @Test
    public void testNullExpression() {
        Object result = evaluator.evaluateExpression(null, exchange, requestBody);
        assertNull(result);
    }
    
    @Test
    public void testEmptyExpression() {
        Object result = evaluator.evaluateExpression("", exchange, requestBody);
        assertNull(result);
    }
    
    @Test
    public void testNonExistentField() {
        Object result = evaluator.evaluateExpression("body.nonExistent", exchange, requestBody);
        assertNull(result);
    }
}
